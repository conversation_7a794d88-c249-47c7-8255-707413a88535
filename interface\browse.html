<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browse All Product Groups - Price Comparison</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Inter Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f3f4f6;
            color: #333;
        }
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 1.5rem;
        }
        .product-card {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .cheapest-highlight {
            border-left: 4px solid #10b981;
            background-color: #ecfdf5;
        }
    </style>
</head>
<body class="p-4 sm:p-6 lg:p-8">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="bg-white p-6 rounded-lg shadow-xl mb-6">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                    <h1 class="text-3xl sm:text-4xl font-bold text-gray-800">Browse Product Groups</h1>
                    <p class="text-gray-600 mt-2">View all product match groups with price comparisons</p>
                </div>
                <div class="flex gap-3">
                    <a href="index.html" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md shadow-md transition duration-200">
                        ← Back to Search
                    </a>
                    <button id="loadDataButton" class="bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-4 rounded-md shadow-md transition duration-200">
                        Load Data
                    </button>
                </div>
            </div>
        </div>

        <!-- Filters and Controls -->
        <div class="bg-white p-4 rounded-lg shadow-md mb-6">
            <div class="flex flex-col sm:flex-row gap-4 items-center">
                <div class="flex-1">
                    <input type="text" id="filterInput" placeholder="Filter by product name..." 
                           class="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div class="flex gap-2">
                    <select id="sortSelect" class="p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        <option value="default">Default Order</option>
                        <option value="price-low">Cheapest First</option>
                        <option value="price-high">Most Expensive First</option>
                        <option value="products-most">Most Products</option>
                        <option value="products-least">Least Products</option>
                    </select>
                    <select id="pageSizeSelect" class="p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        <option value="20">20 per page</option>
                        <option value="50">50 per page</option>
                        <option value="100">100 per page</option>
                        <option value="all">Show All</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Stats -->
        <div id="statsDiv" class="bg-white p-4 rounded-lg shadow-md mb-6 hidden">
            <div class="grid grid-cols-2 sm:grid-cols-4 gap-4 text-center">
                <div>
                    <div class="text-2xl font-bold text-blue-600" id="totalGroups">0</div>
                    <div class="text-sm text-gray-600">Total Groups</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-green-600" id="totalProducts">0</div>
                    <div class="text-sm text-gray-600">Total Products</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-purple-600" id="displayedGroups">0</div>
                    <div class="text-sm text-gray-600">Displayed</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-orange-600" id="avgGroupSize">0</div>
                    <div class="text-sm text-gray-600">Avg Group Size</div>
                </div>
            </div>
        </div>

        <!-- Loading State -->
        <div id="loadingDiv" class="bg-white p-8 rounded-lg shadow-md text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p class="text-gray-600">Click "Load Data" to begin loading product groups...</p>
        </div>

        <!-- Product Groups Container -->
        <div id="productGroupsContainer" class="hidden">
            <div class="card-grid" id="productGrid">
                <!-- Product cards will be inserted here -->
            </div>
            
            <!-- Pagination -->
            <div id="paginationDiv" class="mt-8 flex justify-center">
                <div class="flex gap-2">
                    <button id="prevPageBtn" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 disabled:opacity-50">
                        Previous
                    </button>
                    <span id="pageInfo" class="px-4 py-2 text-gray-700"></span>
                    <button id="nextPageBtn" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 disabled:opacity-50">
                        Next
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let allGroups = [];
        let filteredGroups = [];
        let currentPage = 1;
        let pageSize = 20;
        let isDataLoaded = false;

        // DOM elements
        const loadDataButton = document.getElementById('loadDataButton');
        const loadingDiv = document.getElementById('loadingDiv');
        const productGroupsContainer = document.getElementById('productGroupsContainer');
        const productGrid = document.getElementById('productGrid');
        const filterInput = document.getElementById('filterInput');
        const sortSelect = document.getElementById('sortSelect');
        const pageSizeSelect = document.getElementById('pageSizeSelect');
        const statsDiv = document.getElementById('statsDiv');
        const paginationDiv = document.getElementById('paginationDiv');
        const prevPageBtn = document.getElementById('prevPageBtn');
        const nextPageBtn = document.getElementById('nextPageBtn');
        const pageInfo = document.getElementById('pageInfo');

        /**
         * Fetches JSON data from files
         */
        async function fetchData(type) {
            try {
                const response = await fetch(`./${type}.json`);
                if (!response.ok) {
                    throw new Error(`Failed to fetch ${type}.json: ${response.status}`);
                }
                return await response.json();
            } catch (error) {
                console.error(`Error fetching data for ${type}:`, error);
                throw error;
            }
        }

        /**
         * Gets the best price for comparison based on seller-specific logic
         */
        function getBestComparisonPrice(product, seller) {
            const parsePrice = (price) => {
                if (typeof price === 'number') return price;
                if (typeof price === 'string') {
                    const parsed = parseFloat(price);
                    return isNaN(parsed) ? null : parsed;
                }
                return null;
            };

            let comparisonPrice = null;
            let priceType = '';

            switch (seller) {
                case 'chef':
                    comparisonPrice = parsePrice(product.unitportionprice) || 
                                    parsePrice(product.caseportionprice) || 
                                    parsePrice(product.unitprice) || 
                                    parsePrice(product.caseprice);
                    priceType = product.unitportionprice ? 'unitportionprice' : 
                               product.caseportionprice ? 'caseportionprice' :
                               product.unitprice ? 'unitprice' : 'caseprice';
                    break;
                case 'depot':
                    return { comparisonPrice: null, priceType: 'skipped' };
                case 'sham':
                    comparisonPrice = parsePrice(product.portionprice) || 
                                    parsePrice(product.price);
                    priceType = product.portionprice ? 'portionprice' : 'price';
                    break;
                default:
                    comparisonPrice = parsePrice(product.portionprice) || 
                                    parsePrice(product.price);
                    priceType = product.portionprice ? 'portionprice' : 'price';
                    break;
            }

            return { comparisonPrice, priceType };
        }

        /**
         * Loads and processes all product data
         */
        async function loadAllData() {
            try {
                loadingDiv.innerHTML = `
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p class="text-gray-600">Loading matches data...</p>
                `;

                // Load matches data
                const matches = await fetchData('matches');
                
                loadingDiv.innerHTML = `
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p class="text-gray-600">Loading seller data...</p>
                `;

                // Load all seller data
                const sellers = ['usfood', 'perf', 'greco', 'sham', 'chef', 'sysco'];
                const sellerData = {};
                
                for (const seller of sellers) {
                    try {
                        sellerData[seller] = await fetchData(seller);
                    } catch (error) {
                        console.warn(`Could not load ${seller} data:`, error);
                        sellerData[seller] = [];
                    }
                }

                loadingDiv.innerHTML = `
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p class="text-gray-600">Processing ${matches.length} product groups...</p>
                `;

                // Process matches into groups
                allGroups = [];
                let processedCount = 0;

                for (const matchGroup of matches) {
                    const groupId = Object.keys(matchGroup)[0];
                    const groupProducts = [];

                    for (const key in matchGroup) {
                        const [seller, productNumber] = key.split('_');
                        
                        if (seller === 'depot') continue;
                        
                        const productsInSeller = sellerData[seller];
                        if (productsInSeller) {
                            const productDetail = productsInSeller.find(p => 
                                String(p.productnumber) === String(productNumber)
                            );
                            
                            if (productDetail) {
                                const { comparisonPrice, priceType } = getBestComparisonPrice(productDetail, seller);
                                
                                if (comparisonPrice !== null) {
                                    groupProducts.push({
                                        seller: seller,
                                        productnumber: productDetail.productnumber,
                                        name: productDetail.name,
                                        unittype: productDetail.unittype || productDetail.unit || 'unknown',
                                        comparisonPrice: comparisonPrice,
                                        priceType: priceType,
                                        originalProduct: productDetail
                                    });
                                }
                            }
                        }
                    }

                    if (groupProducts.length > 0) {
                        // Find cheapest product
                        let cheapestProduct = groupProducts[0];
                        for (let i = 1; i < groupProducts.length; i++) {
                            if (groupProducts[i].comparisonPrice < cheapestProduct.comparisonPrice) {
                                cheapestProduct = groupProducts[i];
                            }
                        }
                        cheapestProduct.cheapest = true;

                        allGroups.push({
                            id: groupId,
                            products: groupProducts,
                            cheapestPrice: cheapestProduct.comparisonPrice,
                            productCount: groupProducts.length
                        });
                    }

                    processedCount++;
                    if (processedCount % 100 === 0) {
                        loadingDiv.innerHTML = `
                            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                            <p class="text-gray-600">Processed ${processedCount}/${matches.length} groups...</p>
                        `;
                        // Allow UI to update
                        await new Promise(resolve => setTimeout(resolve, 10));
                    }
                }

                filteredGroups = [...allGroups];
                isDataLoaded = true;
                
                // Hide loading, show content
                loadingDiv.classList.add('hidden');
                productGroupsContainer.classList.remove('hidden');
                statsDiv.classList.remove('hidden');
                
                updateStats();
                renderGroups();
                
                console.log(`Loaded ${allGroups.length} product groups`);
                
            } catch (error) {
                console.error('Error loading data:', error);
                loadingDiv.innerHTML = `
                    <div class="text-center">
                        <p class="text-red-500 mb-4">Error loading data: ${error.message}</p>
                        <button onclick="loadAllData()" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md">
                            Try Again
                        </button>
                    </div>
                `;
            }
        }

        /**
         * Updates the statistics display
         */
        function updateStats() {
            const totalProducts = allGroups.reduce((sum, group) => sum + group.productCount, 0);
            const avgGroupSize = allGroups.length > 0 ? (totalProducts / allGroups.length).toFixed(1) : 0;

            document.getElementById('totalGroups').textContent = allGroups.length;
            document.getElementById('totalProducts').textContent = totalProducts;
            document.getElementById('displayedGroups').textContent = filteredGroups.length;
            document.getElementById('avgGroupSize').textContent = avgGroupSize;
        }

        /**
         * Filters groups based on search input
         */
        function filterGroups() {
            const filterText = filterInput.value.toLowerCase().trim();

            if (!filterText) {
                filteredGroups = [...allGroups];
            } else {
                filteredGroups = allGroups.filter(group => {
                    return group.products.some(product =>
                        product.name.toLowerCase().includes(filterText) ||
                        String(product.productnumber).toLowerCase().includes(filterText)
                    );
                });
            }

            currentPage = 1;
            updateStats();
            renderGroups();
        }

        /**
         * Sorts groups based on selected criteria
         */
        function sortGroups() {
            const sortBy = sortSelect.value;

            switch (sortBy) {
                case 'price-low':
                    filteredGroups.sort((a, b) => a.cheapestPrice - b.cheapestPrice);
                    break;
                case 'price-high':
                    filteredGroups.sort((a, b) => b.cheapestPrice - a.cheapestPrice);
                    break;
                case 'products-most':
                    filteredGroups.sort((a, b) => b.productCount - a.productCount);
                    break;
                case 'products-least':
                    filteredGroups.sort((a, b) => a.productCount - b.productCount);
                    break;
                default:
                    // Keep original order
                    break;
            }

            currentPage = 1;
            renderGroups();
        }

        /**
         * Creates a product card HTML
         */
        function createProductCard(group) {
            const cheapestProduct = group.products.find(p => p.cheapest);
            const sortedProducts = [...group.products].sort((a, b) => a.comparisonPrice - b.comparisonPrice);

            let productsHtml = '';
            sortedProducts.forEach(product => {
                const highlightClass = product.cheapest ? 'cheapest-highlight' : '';
                const badge = product.cheapest ? '<span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full ml-2">🏆 Cheapest</span>' : '';

                productsHtml += `
                    <div class="p-3 border-b border-gray-100 last:border-b-0 ${highlightClass}">
                        <div class="flex justify-between items-start">
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 truncate">${product.seller.toUpperCase()}</p>
                                <p class="text-xs text-gray-600 truncate" title="${product.name}">${product.name}</p>
                                <p class="text-xs text-gray-500">#${product.productnumber} | ${product.unittype}</p>
                            </div>
                            <div class="text-right ml-2">
                                <p class="text-sm font-bold text-gray-900">$${product.comparisonPrice.toFixed(2)}</p>
                                <p class="text-xs text-gray-500">${product.priceType}</p>
                                ${badge}
                            </div>
                        </div>
                    </div>
                `;
            });

            return `
                <div class="product-card bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="p-4 bg-gray-50 border-b">
                        <div class="flex justify-between items-start">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-800 mb-1">Product Group</h3>
                                <p class="text-sm text-gray-600">${group.productCount} products • Best: $${group.cheapestPrice.toFixed(2)}</p>
                            </div>
                            <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                                ${group.id.split('_')[0].toUpperCase()}
                            </span>
                        </div>
                    </div>
                    <div class="max-h-64 overflow-y-auto">
                        ${productsHtml}
                    </div>
                </div>
            `;
        }

        /**
         * Renders the current page of groups
         */
        function renderGroups() {
            if (!isDataLoaded) return;

            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = pageSize === 'all' ? filteredGroups.length : startIndex + parseInt(pageSize);
            const pageGroups = filteredGroups.slice(startIndex, endIndex);

            productGrid.innerHTML = '';

            if (pageGroups.length === 0) {
                productGrid.innerHTML = `
                    <div class="col-span-full text-center py-12">
                        <p class="text-gray-500 text-lg">No product groups found</p>
                        <p class="text-gray-400 text-sm mt-2">Try adjusting your filter or sort criteria</p>
                    </div>
                `;
                return;
            }

            pageGroups.forEach(group => {
                const cardElement = document.createElement('div');
                cardElement.innerHTML = createProductCard(group);
                productGrid.appendChild(cardElement.firstElementChild);
            });

            updatePagination();
        }

        /**
         * Updates pagination controls
         */
        function updatePagination() {
            if (pageSize === 'all') {
                paginationDiv.classList.add('hidden');
                return;
            }

            paginationDiv.classList.remove('hidden');

            const totalPages = Math.ceil(filteredGroups.length / pageSize);

            prevPageBtn.disabled = currentPage <= 1;
            nextPageBtn.disabled = currentPage >= totalPages;

            pageInfo.textContent = `Page ${currentPage} of ${totalPages} (${filteredGroups.length} groups)`;
        }

        /**
         * Changes page size and re-renders
         */
        function changePageSize() {
            pageSize = pageSizeSelect.value === 'all' ? 'all' : parseInt(pageSizeSelect.value);
            currentPage = 1;
            renderGroups();
        }

        /**
         * Goes to previous page
         */
        function previousPage() {
            if (currentPage > 1) {
                currentPage--;
                renderGroups();
            }
        }

        /**
         * Goes to next page
         */
        function nextPage() {
            const totalPages = Math.ceil(filteredGroups.length / pageSize);
            if (currentPage < totalPages) {
                currentPage++;
                renderGroups();
            }
        }

        // Event listeners
        loadDataButton.addEventListener('click', loadAllData);
        filterInput.addEventListener('input', filterGroups);
        sortSelect.addEventListener('change', sortGroups);
        pageSizeSelect.addEventListener('change', changePageSize);
        prevPageBtn.addEventListener('click', previousPage);
        nextPageBtn.addEventListener('click', nextPage);

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Browse page initialized');
        });
    </script>
</body>
</html>
