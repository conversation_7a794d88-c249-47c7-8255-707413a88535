<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Price Comparison</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Inter Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f3f4f6;
            color: #333;
        }
    </style>
</head>
<body class="p-4 sm:p-6 lg:p-8">
    <div class="max-w-4xl mx-auto bg-white p-6 sm:p-8 rounded-lg shadow-xl">
        <h1 class="text-3xl sm:text-4xl font-bold text-center mb-6 text-gray-800">Product Price Comparison</h1>

        <!-- Search Section -->
        <div class="mb-8 p-4 bg-gray-50 rounded-lg border border-gray-200">
            <h2 class="text-xl font-semibold mb-4 text-gray-700">Find a Product</h2>
            <div class="flex flex-col sm:flex-row gap-4 mb-4">
                <select id="sellerSelect" class="flex-grow p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 text-gray-700">
                    <option value="">Select Seller</option>
                    <option value="usfood">Usfood</option>
                    <option value="perf">Perf</option>
                    <option value="greco">Greco</option>
                    <option value="sham">Sham</option>
                    <option value="chef">Chef</option>
                    <option value="sysco">Sysco</option>
                </select>
                <input type="text" id="searchInput" placeholder="Search by name or number..." class="flex-grow p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 text-gray-700">
                <button id="searchButton" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-md shadow-md transition duration-200 ease-in-out transform hover:scale-105">
                    Search
                </button>
            </div>
            <div id="searchResults" class="mt-4 border border-gray-200 rounded-md bg-white overflow-hidden max-h-60 overflow-y-auto">
                <!-- Search results will be displayed here -->
            </div>
        </div>

        <!-- Comparison Results Section -->
        <div id="comparisonResults" class="mt-8">
            <h2 class="text-xl font-semibold mb-4 text-gray-700">Comparison Details</h2>
            <div id="matchGroupCard" class="bg-white rounded-lg shadow-lg overflow-hidden">
                <!-- Match group comparison card will be displayed here -->
                <p class="p-6 text-gray-500 text-center">Search for a product to see its comparison group.</p>
            </div>
        </div>

        <!-- Navigation and Export Section -->
        <div class="mt-8 p-4 bg-gray-50 rounded-lg border border-gray-200">
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <a href="browse.html" class="bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-md shadow-md transition duration-200 ease-in-out transform hover:scale-105">
                    Browse All Products
                </a>
                <button id="exportDataButton" class="bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 px-6 rounded-md shadow-md transition duration-200 ease-in-out transform hover:scale-105">
                    Export Comparison Data
                </button>
            </div>
            <p class="text-sm text-gray-600 mt-2 text-center">Browse all product groups or export the pre-computed comparison data</p>
        </div>
    </div>

    <script>
        // Global variables for data storage
        let matchesData = [];
        let sellerData = {};
        let precomputedMatches = {}; // Stores the pre-processed match groups
        let isDataLoaded = false;

        /**
         * Fetches JSON data from files using the fetch API.
         * @param {string} type - 'matches' or a seller name like 'usfood'.
         * @returns {Promise<Array|Object>} - A promise resolving with the data.
         */
        async function fetchData(type) {
            try {
                if (type === 'matches') {
                    if (matchesData.length === 0) {
                        const response = await fetch('./matches.json');
                        if (!response.ok) {
                            throw new Error(`Failed to fetch matches.json: ${response.status}`);
                        }
                        matchesData = await response.json();
                    }
                    return matchesData;
                } else {
                    // Fetch seller data
                    if (!sellerData[type]) {
                        const response = await fetch(`./${type}.json`);
                        if (!response.ok) {
                            throw new Error(`Failed to fetch ${type}.json: ${response.status}`);
                        }
                        sellerData[type] = await response.json();
                    }
                    return sellerData[type];
                }
            } catch (error) {
                console.error(`Error fetching data for ${type}:`, error);
                return null;
            }
        }

        /**
         * Loads all seller data files to ensure they're available for search and comparison
         */
        async function loadAllSellerData() {
            const sellers = ['usfood', 'perf', 'greco', 'sham', 'chef', 'sysco'];
            const loadPromises = sellers.map(seller => fetchData(seller));

            try {
                await Promise.all(loadPromises);
                console.log('All seller data loaded successfully');
                isDataLoaded = true;
            } catch (error) {
                console.error('Error loading seller data:', error);
                throw error;
            }
        }

        /**
         * Gets the best price for comparison based on seller-specific logic
         */
        function getBestComparisonPrice(product, seller) {
            // Convert string prices to numbers
            const parsePrice = (price) => {
                if (typeof price === 'number') return price;
                if (typeof price === 'string') {
                    const parsed = parseFloat(price);
                    return isNaN(parsed) ? null : parsed;
                }
                return null;
            };

            let comparisonPrice = null;
            let priceType = '';

            // Seller-specific price priority logic
            switch (seller) {
                case 'chef':
                    // Chef: prioritize unitportionprice/caseportionprice, then unitprice/caseprice
                    comparisonPrice = parsePrice(product.unitportionprice) ||
                                    parsePrice(product.caseportionprice) ||
                                    parsePrice(product.unitprice) ||
                                    parsePrice(product.caseprice);
                    priceType = product.unitportionprice ? 'unitportionprice' :
                               product.caseportionprice ? 'caseportionprice' :
                               product.unitprice ? 'unitprice' : 'caseprice';
                    break;

                case 'depot':
                    // Skip depot products as per requirements
                    return { comparisonPrice: null, priceType: 'skipped' };

                case 'sham':
                    // Sham: portionprice then other prices
                    comparisonPrice = parsePrice(product.portionprice) ||
                                    parsePrice(product.price);
                    priceType = product.portionprice ? 'portionprice' : 'price';
                    break;

                default:
                    // Other sellers: portionprice then price
                    comparisonPrice = parsePrice(product.portionprice) ||
                                    parsePrice(product.price);
                    priceType = product.portionprice ? 'portionprice' : 'price';
                    break;
            }

            return { comparisonPrice, priceType };
        }

        /**
         * Pre-computes the comparison results for all matching groups.
         * This function should ideally be run once, e.g., server-side or on app initialization.
         */
        async function precomputeComparisonResults() {
            console.log("Starting pre-computation...");

            // Ensure all data is loaded first
            if (!isDataLoaded) {
                await loadAllSellerData();
            }

            const matches = await fetchData('matches');
            if (!matches) {
                console.error("Failed to load matches data.");
                return;
            }

            let processedGroups = 0;
            const totalGroups = matches.length;

            for (const matchGroup of matches) {
                // Generate a unique ID for the match group using first product key
                const matchGroupId = Object.keys(matchGroup)[0];
                const groupProducts = [];

                // Iterate over each product within a match group
                for (const key in matchGroup) {
                    const [seller, productNumber] = key.split('_');

                    // Skip depot products
                    if (seller === 'depot') {
                        continue;
                    }

                    const productsInSeller = sellerData[seller];
                    if (productsInSeller) {
                        const productDetail = productsInSeller.find(p =>
                            String(p.productnumber) === String(productNumber)
                        );

                        if (productDetail) {
                            const { comparisonPrice, priceType } = getBestComparisonPrice(productDetail, seller);

                            if (comparisonPrice !== null) {
                                groupProducts.push({
                                    seller: seller,
                                    productnumber: productDetail.productnumber,
                                    name: productDetail.name,
                                    unittype: productDetail.unittype || productDetail.unit || 'unknown',
                                    comparisonPrice: comparisonPrice,
                                    priceType: priceType,
                                    originalProduct: productDetail // Keep reference to original data
                                });
                            }
                        } else {
                            console.warn(`Product ${productNumber} not found in ${seller} data.`);
                        }
                    } else {
                        console.warn(`Seller data for ${seller} could not be loaded.`);
                    }
                }

                // Find the cheapest product in the group
                if (groupProducts.length > 0) {
                    let cheapestProduct = groupProducts[0];
                    for (let i = 1; i < groupProducts.length; i++) {
                        if (groupProducts[i].comparisonPrice < cheapestProduct.comparisonPrice) {
                            cheapestProduct = groupProducts[i];
                        }
                    }
                    cheapestProduct.cheapest = true;
                }

                precomputedMatches[matchGroupId] = groupProducts;
                processedGroups++;

                // Update progress every 100 groups
                if (processedGroups % 100 === 0) {
                    console.log(`Processed ${processedGroups}/${totalGroups} groups...`);
                }
            }

            console.log(`Pre-computation complete. Processed ${processedGroups} groups.`);

            // Save to comparison.json file
            await saveComparisonResults();
        }

        /**
         * Saves the precomputed comparison results to a JSON file
         */
        async function saveComparisonResults() {
            try {
                const dataStr = JSON.stringify(precomputedMatches, null, 2);
                const blob = new Blob([dataStr], { type: "application/json" });
                const url = URL.createObjectURL(blob);

                const a = document.createElement('a');
                a.href = url;
                a.download = 'comparison.json';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                console.log("Comparison results saved to comparison.json");
            } catch (error) {
                console.error("Error saving comparison results:", error);
            }
        }

        // --- DOM Elements ---
        const sellerSelect = document.getElementById('sellerSelect');
        const searchInput = document.getElementById('searchInput');
        const searchButton = document.getElementById('searchButton');
        const searchResultsDiv = document.getElementById('searchResults');
        const matchGroupCardDiv = document.getElementById('matchGroupCard');
        const exportDataButton = document.getElementById('exportDataButton'); // New export button

        /**
         * Handles the search button click and displays matching products from the selected seller.
         */
        async function handleSearch() {
            const selectedSeller = sellerSelect.value;
            const query = searchInput.value.toLowerCase().trim();

            searchResultsDiv.innerHTML = ''; // Clear previous search results

            console.log(`Search initiated: Seller='${selectedSeller}', Query='${query}'`);

            if (!selectedSeller) {
                searchResultsDiv.innerHTML = '<p class="p-4 text-red-500">Please select a seller.</p>';
                console.warn("Search aborted: No seller selected.");
                return;
            }
            if (!query) {
                searchResultsDiv.innerHTML = '<p class="p-4 text-gray-500">Please enter a product name or number.</p>';
                console.warn("Search aborted: Empty query.");
                return;
            }

            // Show loading indicator
            searchResultsDiv.innerHTML = '<p class="p-4 text-blue-500">Searching...</p>';

            const sellerProducts = await fetchData(selectedSeller);
            if (!sellerProducts) {
                searchResultsDiv.innerHTML = `<p class="p-4 text-red-500">Could not load products for ${selectedSeller}.</p>`;
                console.error(`Failed to load products for seller: ${selectedSeller}`);
                return;
            }

            // Robust search logic: check if all query words are present in name or product number
            const keywords = query.split(' ').filter(word => word.length > 0);

            const filteredProducts = sellerProducts.filter(p => {
                const productName = (p.name || '').toLowerCase();
                const productNumberStr = String(p.productnumber || '').toLowerCase();

                const isMatch = keywords.every(keyword =>
                    productName.includes(keyword) || productNumberStr.includes(keyword)
                );

                return isMatch;
            });

            console.log(`Found ${filteredProducts.length} matching products`);

            if (filteredProducts.length === 0) {
                searchResultsDiv.innerHTML = '<p class="p-4 text-gray-500">No products found for your search.</p>';
                return;
            }

            // Limit results to prevent UI overload
            const maxResults = 50;
            const displayProducts = filteredProducts.slice(0, maxResults);

            const ul = document.createElement('ul');
            ul.className = 'divide-y divide-gray-100';

            displayProducts.forEach(product => {
                const li = document.createElement('li');
                li.className = 'p-3 hover:bg-gray-50 cursor-pointer transition duration-150 ease-in-out';

                // Get price info for display
                const { comparisonPrice, priceType } = getBestComparisonPrice(product, selectedSeller);
                const priceDisplay = comparisonPrice ? `$${comparisonPrice.toFixed(2)} (${priceType})` : 'Price N/A';

                li.innerHTML = `
                    <p class="font-medium text-gray-800">${product.name || 'N/A'}</p>
                    <p class="text-sm text-gray-500">Product #: ${product.productnumber || 'N/A'} | Unit: ${product.unittype || product.unit || 'N/A'}</p>
                    <p class="text-sm text-blue-600">${priceDisplay}</p>
                `;
                li.addEventListener('click', () => handleProductClick(product.productnumber, selectedSeller));
                ul.appendChild(li);
            });

            searchResultsDiv.innerHTML = '';
            if (filteredProducts.length > maxResults) {
                const notice = document.createElement('p');
                notice.className = 'p-2 text-sm text-orange-600 bg-orange-50 border-b';
                notice.textContent = `Showing first ${maxResults} of ${filteredProducts.length} results. Refine your search for more specific results.`;
                searchResultsDiv.appendChild(notice);
            }

            searchResultsDiv.appendChild(ul);
        }

        /**
         * Handles the click on a search result product.
         * Finds the corresponding match group and displays its comparison.
         * @param {string} productNumber - The product number of the clicked product.
         * @param {string} sellerName - The seller name of the clicked product.
         */
        async function handleProductClick(productNumber, sellerName) {
            console.log(`Product clicked: Product #: ${productNumber}, Seller: ${sellerName}`);

            // Show loading message
            matchGroupCardDiv.innerHTML = '<p class="p-6 text-blue-500 text-center">Loading comparison group...</p>';

            // Find the match group that contains this product
            let foundMatchGroup = null;
            let matchGroupId = null;

            // Search through matches data to find the group containing this product
            const matches = await fetchData('matches');
            if (!matches) {
                matchGroupCardDiv.innerHTML = `<p class="p-6 text-red-500 text-center">Could not load matches data.</p>`;
                return;
            }

            for (const group of matches) {
                const searchKey = `${sellerName}_${productNumber}`;
                if (group.hasOwnProperty(searchKey)) {
                    matchGroupId = Object.keys(group)[0]; // Use first product key as group ID
                    foundMatchGroup = precomputedMatches[matchGroupId];
                    console.log(`Found match group ID: ${matchGroupId}`, foundMatchGroup);
                    break;
                }
            }

            if (foundMatchGroup && foundMatchGroup.length > 0) {
                displayMatchGroup(foundMatchGroup, matchGroupId);
            } else {
                // If not found in precomputed, try to compute on the fly
                console.log('Group not found in precomputed matches, computing on the fly...');
                await computeGroupOnTheFly(productNumber, sellerName);
            }

            // Clear search results after a product is clicked
            searchResultsDiv.innerHTML = '';
            searchInput.value = ''; // Optionally clear the search input too
        }

        /**
         * Computes a match group on the fly if not found in precomputed data
         */
        async function computeGroupOnTheFly(productNumber, sellerName) {
            const matches = await fetchData('matches');
            if (!matches) {
                matchGroupCardDiv.innerHTML = `<p class="p-6 text-red-500 text-center">Could not load matches data.</p>`;
                return;
            }

            // Find the raw match group
            let rawGroup = null;
            for (const group of matches) {
                const searchKey = `${sellerName}_${productNumber}`;
                if (group.hasOwnProperty(searchKey)) {
                    rawGroup = group;
                    break;
                }
            }

            if (!rawGroup) {
                matchGroupCardDiv.innerHTML = `<p class="p-6 text-red-500 text-center">No matching group found for this product.</p>`;
                return;
            }

            // Compute the group products
            const groupProducts = [];
            for (const key in rawGroup) {
                const [seller, prodNum] = key.split('_');

                if (seller === 'depot') continue; // Skip depot

                const sellerProducts = await fetchData(seller);
                if (sellerProducts) {
                    const productDetail = sellerProducts.find(p =>
                        String(p.productnumber) === String(prodNum)
                    );

                    if (productDetail) {
                        const { comparisonPrice, priceType } = getBestComparisonPrice(productDetail, seller);

                        if (comparisonPrice !== null) {
                            groupProducts.push({
                                seller: seller,
                                productnumber: productDetail.productnumber,
                                name: productDetail.name,
                                unittype: productDetail.unittype || productDetail.unit || 'unknown',
                                comparisonPrice: comparisonPrice,
                                priceType: priceType,
                                originalProduct: productDetail
                            });
                        }
                    }
                }
            }

            // Find cheapest
            if (groupProducts.length > 0) {
                let cheapestProduct = groupProducts[0];
                for (let i = 1; i < groupProducts.length; i++) {
                    if (groupProducts[i].comparisonPrice < cheapestProduct.comparisonPrice) {
                        cheapestProduct = groupProducts[i];
                    }
                }
                cheapestProduct.cheapest = true;

                displayMatchGroup(groupProducts, `${sellerName}_${productNumber}`);
            } else {
                matchGroupCardDiv.innerHTML = `<p class="p-6 text-red-500 text-center">No valid products found in matching group.</p>`;
            }
        }

        /**
         * Displays the details of a matching product group, including comparison.
         * @param {Array<Object>} group - An array of product objects within a match group.
         * @param {string} groupId - The ID of the match group.
         */
        function displayMatchGroup(group, groupId) {
            console.log("Displaying match group:", group);
            if (!group || group.length === 0) {
                matchGroupCardDiv.innerHTML = `<p class="p-6 text-gray-500 text-center">No products in this match group.</p>`;
                return;
            }

            // Sort group by comparison price for better display
            const sortedGroup = [...group].sort((a, b) => a.comparisonPrice - b.comparisonPrice);

            let html = `
                <div class="p-6">
                    <h3 class="text-2xl font-semibold mb-4 text-gray-800">Matching Products Comparison</h3>
                    <p class="text-sm text-gray-600 mb-4">Group ID: ${groupId} | ${group.length} products found</p>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider rounded-tl-lg">Seller</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product Name</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product #</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Type</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Comparison Price</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider rounded-tr-lg">Additional Info</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
            `;

            sortedGroup.forEach(product => {
                let cheapestBadge = '';
                // Apply a distinct background to the cheapest product's row
                const rowBackgroundColor = product.cheapest ? 'bg-green-50 border-l-4 border-green-400' : '';

                if (product.cheapest) {
                    cheapestBadge = `<span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">🏆 Cheapest</span>`;
                }

                // Get additional price information from original product
                const originalProduct = product.originalProduct;
                const additionalInfo = [];

                if (originalProduct) {
                    // Add pack size if available
                    if (originalProduct.packSize) {
                        additionalInfo.push(`Pack: ${originalProduct.packSize}`);
                    }

                    // Add other price types
                    const priceFields = ['unitprice', 'caseprice', 'price', 'unitportionprice', 'caseportionprice'];
                    priceFields.forEach(field => {
                        if (originalProduct[field] && field !== product.priceType) {
                            const price = parseFloat(originalProduct[field]);
                            if (!isNaN(price)) {
                                additionalInfo.push(`${field}: $${price.toFixed(2)}`);
                            }
                        }
                    });
                }

                const formattedComparisonPrice = product.comparisonPrice ? product.comparisonPrice.toFixed(2) : 'N/A';

                html += `
                    <tr class="${rowBackgroundColor}">
                        <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 capitalize">${product.seller}</td>
                        <td class="px-4 py-3 text-sm text-gray-800" style="max-width: 300px; word-wrap: break-word;">${product.name || 'N/A'}</td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-600">${product.productnumber || 'N/A'}</td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-600">${product.unittype || 'N/A'}</td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 font-bold">
                            $${formattedComparisonPrice}
                            <br><span class="text-xs text-gray-500">(${product.priceType})</span>
                            ${cheapestBadge}
                        </td>
                        <td class="px-4 py-3 text-sm text-gray-600" style="max-width: 200px; word-wrap: break-word;">
                            ${additionalInfo.join('<br>') || 'N/A'}
                        </td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
                <div class="p-4 bg-gray-50 text-sm text-gray-600">
                    <p><strong>Note:</strong> Comparison prices are selected based on seller-specific pricing logic. The cheapest product is highlighted with a green background.</p>
                </div>
            `;
            matchGroupCardDiv.innerHTML = html;
        }

        /**
         * Exports the pre-computed comparison data to a JSON file.
         */
        function exportPrecomputedData() {
            if (Object.keys(precomputedMatches).length === 0) {
                alert("No pre-computed data available to export. Please wait for the data to load or refresh the page.");
                return;
            }

            // Create export data with metadata
            const exportData = {
                metadata: {
                    exportDate: new Date().toISOString(),
                    totalGroups: Object.keys(precomputedMatches).length,
                    totalProducts: Object.values(precomputedMatches).reduce((sum, group) => sum + group.length, 0),
                    version: "1.0"
                },
                comparisonGroups: precomputedMatches
            };

            const dataStr = JSON.stringify(exportData, null, 2); // Pretty print JSON
            const blob = new Blob([dataStr], { type: "application/json" });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = 'comparison.json';
            document.body.appendChild(a); // Append to body to make it clickable
            a.click(); // Trigger the download
            document.body.removeChild(a); // Clean up
            URL.revokeObjectURL(url); // Release the object URL

            console.log(`Exported ${exportData.metadata.totalGroups} groups with ${exportData.metadata.totalProducts} products to comparison.json`);

            // Show success message
            const originalText = document.getElementById('exportDataButton').textContent;
            document.getElementById('exportDataButton').textContent = 'Exported!';
            document.getElementById('exportDataButton').classList.add('bg-green-600');
            document.getElementById('exportDataButton').classList.remove('bg-purple-600');

            setTimeout(() => {
                document.getElementById('exportDataButton').textContent = originalText;
                document.getElementById('exportDataButton').classList.remove('bg-green-600');
                document.getElementById('exportDataButton').classList.add('bg-purple-600');
            }, 2000);
        }


        // --- Initialization ---
        document.addEventListener('DOMContentLoaded', async () => {
            // Display a loading message while data is being fetched and pre-computed
            matchGroupCardDiv.innerHTML = `
                <div class="p-6 text-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p class="text-gray-600">Loading data and pre-computing comparisons...</p>
                    <p class="text-sm text-gray-500 mt-2">This may take a moment for large datasets.</p>
                </div>
            `;

            try {
                // Load all seller data first
                await loadAllSellerData();

                // Update loading message
                matchGroupCardDiv.innerHTML = `
                    <div class="p-6 text-center">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                        <p class="text-gray-600">Computing price comparisons...</p>
                        <p class="text-sm text-gray-500 mt-2">Processing product groups...</p>
                    </div>
                `;

                // Run pre-computation on page load
                await precomputeComparisonResults();

                // Clear loading message and set default message
                matchGroupCardDiv.innerHTML = `
                    <div class="p-6 text-center">
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">Ready to Search!</h3>
                        <p class="text-gray-500">Search for a product above to see its comparison group.</p>
                        <p class="text-sm text-gray-400 mt-2">Pre-computed ${Object.keys(precomputedMatches).length} product groups for fast comparison.</p>
                    </div>
                `;

                console.log('Application initialized successfully');

            } catch (error) {
                console.error('Error during initialization:', error);
                matchGroupCardDiv.innerHTML = `
                    <div class="p-6 text-center">
                        <p class="text-red-500">Error loading data: ${error.message}</p>
                        <p class="text-sm text-gray-500 mt-2">Please check the console for more details.</p>
                    </div>
                `;
            }

            // Event listener for explicit search button click
            searchButton.addEventListener('click', handleSearch);

            // Allow pressing Enter key to search
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    handleSearch();
                }
            });

            // Event listener for the new export button
            exportDataButton.addEventListener('click', exportPrecomputedData);
        });
    </script>
</body>
</html>
